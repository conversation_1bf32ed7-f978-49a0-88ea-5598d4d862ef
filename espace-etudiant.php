<?php
require_once 'includes/config.php';

$pageTitle = 'Espace Stagiaire';
$pageDescription = 'Espace dédié aux stagiaires du Pôle Agriculture : ressources pédagogiques, emploi du temps, notes et informations pratiques.';

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="section section-dark">
    <div class="container">
        <div style="max-width: 800px;">
            <h1 style="font-size: var(--font-size-4xl); font-weight: 700; margin-bottom: var(--spacing-6);">
                Espace Stagiaire
            </h1>
            <p class="text-xl">
                Accédez à vos ressources pédagogiques, consultez vos notes et restez informé de la vie du Pôle Agriculture.
            </p>
        </div>
    </div>
</section>

<!-- Section Connexion -->
<section class="section">
    <div class="container">
        <div style="max-width: 500px; margin: 0 auto;">
            <div class="card">
                <div class="card-content">
                    <h2 style="text-align: center; margin-bottom: var(--spacing-6);">Connexion à votre espace</h2>
                    
                    <form id="loginForm">
                        <div style="margin-bottom: var(--spacing-4);">
                            <label for="studentId" style="display: block; margin-bottom: var(--spacing-2); font-weight: 500;">
                                Numéro d'étudiant
                            </label>
                            <input type="text" id="studentId" name="studentId" required
                                   style="width: 100%; padding: var(--spacing-3); border: 1px solid var(--gray-300); border-radius: var(--border-radius); font-size: var(--font-size-base);"
                                   placeholder="Ex: CMC2024001">
                        </div>
                        
                        <div style="margin-bottom: var(--spacing-6);">
                            <label for="password" style="display: block; margin-bottom: var(--spacing-2); font-weight: 500;">
                                Mot de passe
                            </label>
                            <input type="password" id="password" name="password" required
                                   style="width: 100%; padding: var(--spacing-3); border: 1px solid var(--gray-300); border-radius: var(--border-radius); font-size: var(--font-size-base);"
                                   placeholder="Votre mot de passe">
                        </div>
                        
                        <button type="submit" class="btn btn-primary" style="width: 100%; margin-bottom: var(--spacing-4);">
                            Se connecter
                        </button>
                        
                        <div style="text-align: center;">
                            <a href="#" style="color: var(--agro-600); font-size: var(--font-size-sm);">
                                Mot de passe oublié ?
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section Services -->
<section class="section section-bg">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Services disponibles</h2>
            <p class="section-subtitle">
                Découvrez tous les services accessibles dans votre espace personnel
            </p>
        </div>
        
        <div class="grid md:grid-cols-2 lg:grid-cols-3">
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="calendar" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Emploi du temps</h3>
                    <p class="card-description">
                        Consultez votre planning de cours, les horaires des ateliers pratiques et les dates d'examens.
                    </p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="file-text" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Notes et évaluations</h3>
                    <p class="card-description">
                        Accédez à vos notes, bulletins et suivez votre progression tout au long de votre formation.
                    </p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="book-open" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Ressources pédagogiques</h3>
                    <p class="card-description">
                        Téléchargez vos cours, exercices, supports de formation et documentation technique.
                    </p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="users" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Forum étudiant</h3>
                    <p class="card-description">
                        Échangez avec vos camarades, posez vos questions et participez aux discussions de groupe.
                    </p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="briefcase" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Stages et emploi</h3>
                    <p class="card-description">
                        Trouvez des offres de stage, consultez les opportunités d'emploi et gérez vos candidatures.
                    </p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="bell" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Notifications</h3>
                    <p class="card-description">
                        Recevez les annonces importantes, les changements d'horaires et les informations administratives.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section Aide -->
<section class="section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Besoin d'aide ?</h2>
            <p class="section-subtitle">
                Notre équipe est là pour vous accompagner
            </p>
        </div>
        
        <div class="grid md:grid-cols-3">
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="help-circle" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">FAQ</h3>
                    <p class="card-description">
                        Consultez les questions fréquemment posées pour trouver rapidement des réponses.
                    </p>
                    <button class="btn btn-secondary" style="margin-top: var(--spacing-4);">
                        Voir la FAQ
                    </button>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="mail" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Support technique</h3>
                    <p class="card-description">
                        Contactez notre équipe technique pour tout problème de connexion ou d'accès.
                    </p>
                    <button class="btn btn-secondary" style="margin-top: var(--spacing-4);">
                        Contacter le support
                    </button>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="phone" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Assistance téléphonique</h3>
                    <p class="card-description">
                        Appelez-nous du lundi au vendredi de 8h à 17h pour une assistance immédiate.
                    </p>
                    <button class="btn btn-secondary" style="margin-top: var(--spacing-4);">
                        +212 5XX XX XX XX
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.getElementById('loginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const studentId = document.getElementById('studentId').value;
    const password = document.getElementById('password').value;
    
    // Simulation de connexion
    if (studentId && password) {
        alert('Fonctionnalité de connexion en cours de développement.\nVeuillez contacter l\'administration pour accéder à votre espace.');
    } else {
        alert('Veuillez remplir tous les champs.');
    }
});
</script>

<?php include 'includes/footer.php'; ?>
