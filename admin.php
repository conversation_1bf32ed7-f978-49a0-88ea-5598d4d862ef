<?php
require_once 'includes/config.php';

$pageTitle = 'Administration';
$pageDescription = 'Espace d\'administration du Pôle Agriculture - Gestion des formations, des étudiants et du contenu du site.';

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="section section-dark">
    <div class="container">
        <div style="max-width: 800px;">
            <h1 style="font-size: var(--font-size-4xl); font-weight: 700; margin-bottom: var(--spacing-6);">
                Administration
            </h1>
            <p class="text-xl">
                Espace réservé à l'équipe administrative pour la gestion du Pôle Agriculture.
            </p>
        </div>
    </div>
</section>

<!-- Section Connexion Admin -->
<section class="section">
    <div class="container">
        <div style="max-width: 500px; margin: 0 auto;">
            <div class="card">
                <div class="card-content">
                    <h2 style="text-align: center; margin-bottom: var(--spacing-6);">Connexion Administrateur</h2>
                    
                    <form id="adminLoginForm">
                        <div style="margin-bottom: var(--spacing-4);">
                            <label for="adminUsername" style="display: block; margin-bottom: var(--spacing-2); font-weight: 500;">
                                Nom d'utilisateur
                            </label>
                            <input type="text" id="adminUsername" name="adminUsername" required
                                   style="width: 100%; padding: var(--spacing-3); border: 1px solid var(--gray-300); border-radius: var(--border-radius); font-size: var(--font-size-base);"
                                   placeholder="Votre nom d'utilisateur">
                        </div>
                        
                        <div style="margin-bottom: var(--spacing-6);">
                            <label for="adminPassword" style="display: block; margin-bottom: var(--spacing-2); font-weight: 500;">
                                Mot de passe
                            </label>
                            <input type="password" id="adminPassword" name="adminPassword" required
                                   style="width: 100%; padding: var(--spacing-3); border: 1px solid var(--gray-300); border-radius: var(--border-radius); font-size: var(--font-size-base);"
                                   placeholder="Votre mot de passe">
                        </div>
                        
                        <button type="submit" class="btn btn-primary" style="width: 100%; margin-bottom: var(--spacing-4);">
                            Se connecter
                        </button>
                        
                        <div style="text-align: center;">
                            <p style="color: var(--gray-600); font-size: var(--font-size-sm);">
                                Accès réservé au personnel autorisé
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section Fonctionnalités Admin -->
<section class="section section-bg">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Fonctionnalités d'administration</h2>
            <p class="section-subtitle">
                Outils de gestion pour l'équipe administrative
            </p>
        </div>
        
        <div class="grid md:grid-cols-2 lg:grid-cols-3">
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="users" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Gestion des étudiants</h3>
                    <p class="card-description">
                        Inscription, suivi des étudiants, gestion des notes et des absences.
                    </p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="book-open" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Gestion des formations</h3>
                    <p class="card-description">
                        Création et modification des programmes, planification des cours et des examens.
                    </p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="calendar" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Planning et horaires</h3>
                    <p class="card-description">
                        Gestion des emplois du temps, réservation des salles et des équipements.
                    </p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="file-text" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Gestion du contenu</h3>
                    <p class="card-description">
                        Mise à jour des actualités, gestion des ressources pédagogiques et du site web.
                    </p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="bar-chart" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Statistiques et rapports</h3>
                    <p class="card-description">
                        Génération de rapports, statistiques de fréquentation et analyses de performance.
                    </p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background-color: var(--agro-100); padding: var(--spacing-3); border-radius: 50%; color: var(--agro-600);">
                            <i data-lucide="settings" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Configuration système</h3>
                    <p class="card-description">
                        Paramètres du système, gestion des utilisateurs et configuration des modules.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section Statistiques rapides -->
<section class="section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Tableau de bord</h2>
            <p class="section-subtitle">
                Aperçu rapide des données importantes
            </p>
        </div>
        
        <div class="grid sm:grid-cols-2 lg:grid-cols-4">
            <div class="stats-card">
                <div class="stats-icon">
                    <i data-lucide="users"></i>
                </div>
                <div class="stats-value">523</div>
                <div class="stats-label">Étudiants inscrits</div>
            </div>
            
            <div class="stats-card">
                <div class="stats-icon">
                    <i data-lucide="book-open"></i>
                </div>
                <div class="stats-value">12</div>
                <div class="stats-label">Formations actives</div>
            </div>
            
            <div class="stats-card">
                <div class="stats-icon">
                    <i data-lucide="user-check"></i>
                </div>
                <div class="stats-value">45</div>
                <div class="stats-label">Formateurs</div>
            </div>
            
            <div class="stats-card">
                <div class="stats-icon">
                    <i data-lucide="award"></i>
                </div>
                <div class="stats-value">89%</div>
                <div class="stats-label">Taux de réussite</div>
            </div>
        </div>
    </div>
</section>

<!-- Section Accès rapide -->
<section class="section section-bg">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Accès rapide</h2>
            <p class="section-subtitle">
                Liens vers les fonctionnalités les plus utilisées
            </p>
        </div>
        
        <div class="grid md:grid-cols-2 lg:grid-cols-4">
            <button class="card" style="border: none; background: var(--white); cursor: pointer; text-align: center;">
                <div class="card-content">
                    <i data-lucide="plus-circle" style="width: 2rem; height: 2rem; color: var(--agro-600); margin-bottom: var(--spacing-2);"></i>
                    <h4 style="font-weight: 600; margin-bottom: var(--spacing-1);">Nouvel étudiant</h4>
                    <p style="color: var(--gray-600); font-size: var(--font-size-sm);">Inscrire un nouvel étudiant</p>
                </div>
            </button>
            
            <button class="card" style="border: none; background: var(--white); cursor: pointer; text-align: center;">
                <div class="card-content">
                    <i data-lucide="edit" style="width: 2rem; height: 2rem; color: var(--agro-600); margin-bottom: var(--spacing-2);"></i>
                    <h4 style="font-weight: 600; margin-bottom: var(--spacing-1);">Nouvelle actualité</h4>
                    <p style="color: var(--gray-600); font-size: var(--font-size-sm);">Publier une actualité</p>
                </div>
            </button>
            
            <button class="card" style="border: none; background: var(--white); cursor: pointer; text-align: center;">
                <div class="card-content">
                    <i data-lucide="calendar-plus" style="width: 2rem; height: 2rem; color: var(--agro-600); margin-bottom: var(--spacing-2);"></i>
                    <h4 style="font-weight: 600; margin-bottom: var(--spacing-1);">Planifier cours</h4>
                    <p style="color: var(--gray-600); font-size: var(--font-size-sm);">Ajouter au planning</p>
                </div>
            </button>
            
            <button class="card" style="border: none; background: var(--white); cursor: pointer; text-align: center;">
                <div class="card-content">
                    <i data-lucide="download" style="width: 2rem; height: 2rem; color: var(--agro-600); margin-bottom: var(--spacing-2);"></i>
                    <h4 style="font-weight: 600; margin-bottom: var(--spacing-1);">Exporter données</h4>
                    <p style="color: var(--gray-600); font-size: var(--font-size-sm);">Générer un rapport</p>
                </div>
            </button>
        </div>
    </div>
</section>

<script>
document.getElementById('adminLoginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const username = document.getElementById('adminUsername').value;
    const password = document.getElementById('adminPassword').value;
    
    // Simulation de connexion admin
    if (username && password) {
        alert('Interface d\'administration en cours de développement.\nContactez l\'équipe technique pour plus d\'informations.');
    } else {
        alert('Veuillez remplir tous les champs.');
    }
});

// Gestion des boutons d'accès rapide
document.querySelectorAll('.card button, button.card').forEach(button => {
    button.addEventListener('click', function() {
        const action = this.querySelector('h4').textContent;
        alert(`Fonctionnalité "${action}" en cours de développement.`);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
