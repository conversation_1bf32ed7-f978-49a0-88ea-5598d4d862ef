# Pôle Agriculture - CMC (Version PHP)

Site web du Pôle Agriculture de la Cité des Métiers et des Compétences (CMC) développé en PHP, CSS et HTML.

## 📋 Description

Ce projet est une version PHP pure du site web du Pôle Agriculture de la CMC, convertie depuis la version React/TypeScript originale. Le site présente les formations, actualités et services du pôle agriculture.

## 🚀 Fonctionnalités

### Pages principales
- **Accueil** (`index.php`) - Page d'accueil avec présentation générale
- **Présentation** (`presentation.php`) - Histoire, mission, équipe et partenaires
- **Formations** (`formations.php`) - Catalogue des formations disponibles
- **Actualités** (`actualites.php`) - Actualités et événements du pôle
- **Espace Stagiaire** (`espace-etudiant.php`) - Interface pour les étudiants
- **Administration** (`admin.php`) - Interface d'administration
- **Page 404** (`404.php`) - Page d'erreur personnalisée

### Fonctionnalités techniques
- Design responsive (mobile-first)
- Navigation avec menu hamburger mobile
- Animations CSS et JavaScript
- Système de tabs interactif
- Barres de progression animées
- Filtrage des actualités par catégorie
- Optimisation SEO
- Compression et cache des ressources

## 🛠️ Technologies utilisées

- **Backend** : PHP 7.4+
- **Frontend** : HTML5, CSS3, JavaScript (Vanilla)
- **Icônes** : Lucide Icons
- **Fonts** : Google Fonts (Inter)
- **Serveur web** : Apache avec mod_rewrite

## 📁 Structure du projet

```
agro-cmc-connect-main/
├── assets/
│   ├── css/
│   │   └── style.css          # Styles CSS principaux
│   ├── js/
│   │   └── script.js          # JavaScript pour l'interactivité
│   └── images/
│       └── logo.svg           # Logo du site
├── includes/
│   ├── config.php             # Configuration et données
│   ├── header.php             # En-tête commun
│   └── footer.php             # Pied de page commun
├── index.php                  # Page d'accueil
├── presentation.php           # Page de présentation
├── formations.php             # Page des formations
├── actualites.php             # Page des actualités
├── espace-etudiant.php        # Espace stagiaire
├── admin.php                  # Interface d'administration
├── 404.php                    # Page d'erreur 404
├── .htaccess                  # Configuration Apache
└── README_PHP.md              # Documentation
```

## ⚙️ Installation

### Prérequis
- Serveur web Apache avec PHP 7.4+
- Module mod_rewrite activé
- XAMPP, WAMP, LAMP ou serveur similaire

### Étapes d'installation

1. **Cloner ou télécharger le projet**
   ```bash
   git clone [URL_DU_REPO]
   cd agro-cmc-connect-main
   ```

2. **Configurer le serveur web**
   - Placer les fichiers dans le répertoire web (ex: `htdocs` pour XAMPP)
   - S'assurer que mod_rewrite est activé

3. **Configuration**
   - Modifier les paramètres dans `includes/config.php` si nécessaire
   - Ajuster l'URL de base selon votre environnement

4. **Accéder au site**
   - Ouvrir `http://localhost/agro-cmc-connect-main` dans votre navigateur

## 🎨 Personnalisation

### Couleurs
Les couleurs principales sont définies dans `assets/css/style.css` :
- Vert principal : `--agro-600: #369c5a`
- Vert foncé : `--agro-800: #1e5232`
- Vert clair : `--agro-100: #dcf4e3`

### Contenu
- Modifier les données dans `includes/config.php`
- Ajouter/modifier les actualités dans `actualites.php`
- Personnaliser les formations dans `formations.php`

### Images
- Remplacer le logo dans `assets/images/logo.svg`
- Utiliser des images optimisées pour le web
- Respecter les droits d'auteur des images

## 📱 Responsive Design

Le site est entièrement responsive avec des breakpoints :
- Mobile : < 768px
- Tablette : 768px - 1024px
- Desktop : > 1024px

## 🔧 Développement

### Structure CSS
- Variables CSS pour la cohérence
- Classes utilitaires pour la rapidité
- Composants modulaires
- Animations et transitions fluides

### JavaScript
- Code vanilla (pas de framework)
- Gestion des événements optimisée
- Animations au scroll
- Navigation mobile interactive

### PHP
- Structure modulaire avec includes
- Configuration centralisée
- Gestion des erreurs
- URLs propres avec .htaccess

## 🚀 Déploiement

### Serveur de production
1. Uploader tous les fichiers sur le serveur
2. Configurer les permissions appropriées
3. Activer HTTPS (recommandé)
4. Configurer la compression et le cache
5. Tester toutes les fonctionnalités

### Optimisations
- Compression GZIP activée
- Cache des ressources statiques
- Images optimisées
- CSS et JS minifiés (optionnel)

## 🔒 Sécurité

- Protection des fichiers sensibles via .htaccess
- Headers de sécurité configurés
- Validation des entrées utilisateur
- Protection contre les injections

## 📞 Support

Pour toute question ou problème :
- Email : <EMAIL>
- Téléphone : +212 5XX XX XX XX

## 📄 Licence

Ce projet est développé pour la Cité des Métiers et des Compétences (CMC) - Pôle Agriculture.

## 🤝 Contribution

Pour contribuer au projet :
1. Fork le repository
2. Créer une branche pour votre fonctionnalité
3. Commiter vos changements
4. Pousser vers la branche
5. Créer une Pull Request

---

**Développé avec ❤️ pour le Pôle Agriculture - CMC**
