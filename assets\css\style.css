/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

:root {
    /* Couleurs principales - Inspirées du design moderne */
    --primary-50: #f0fdf4;
    --primary-100: #dcfce7;
    --primary-200: #bbf7d0;
    --primary-300: #86efac;
    --primary-400: #4ade80;
    --primary-500: #22c55e;
    --primary-600: #16a34a;
    --primary-700: #15803d;
    --primary-800: #166534;
    --primary-900: #14532d;

    /* Couleurs secondaires modernes */
    --secondary-50: #f8fafc;
    --secondary-100: #f1f5f9;
    --secondary-200: #e2e8f0;
    --secondary-300: #cbd5e1;
    --secondary-400: #94a3b8;
    --secondary-500: #64748b;
    --secondary-600: #475569;
    --secondary-700: #334155;
    --secondary-800: #1e293b;
    --secondary-900: #0f172a;

    /* Couleurs accent inspirées du design */
    --accent-orange: #f97316;
    --accent-blue: #3b82f6;
    --accent-purple: #8b5cf6;
    --accent-pink: #ec4899;
    --accent-yellow: #eab308;

    /* Couleurs système - Design moderne */
    --white: #ffffff;
    --black: #000000;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Couleurs de surface inspirées du design */
    --surface-primary: #ffffff;
    --surface-secondary: #f8fafc;
    --surface-tertiary: #f1f5f9;
    --surface-accent: #f0fdf4;

    /* Couleurs de statut */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Typographie moderne inspirée du design */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-secondary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-display: 'Outfit', 'Inter', sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

    /* Échelle typographique moderne */
    --text-xs: 0.75rem;      /* 12px */
    --text-sm: 0.875rem;     /* 14px */
    --text-base: 1rem;       /* 16px */
    --text-lg: 1.125rem;     /* 18px */
    --text-xl: 1.25rem;      /* 20px */
    --text-2xl: 1.5rem;      /* 24px */
    --text-3xl: 1.875rem;    /* 30px */
    --text-4xl: 2.25rem;     /* 36px */
    --text-5xl: 3rem;        /* 48px */
    --text-6xl: 3.75rem;     /* 60px */
    --text-7xl: 4.5rem;      /* 72px */

    /* Poids de police */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;

    /* Système d'espacement moderne */
    --space-0: 0;
    --space-1: 0.25rem;      /* 4px */
    --space-2: 0.5rem;       /* 8px */
    --space-3: 0.75rem;      /* 12px */
    --space-4: 1rem;         /* 16px */
    --space-5: 1.25rem;      /* 20px */
    --space-6: 1.5rem;       /* 24px */
    --space-8: 2rem;         /* 32px */
    --space-10: 2.5rem;      /* 40px */
    --space-12: 3rem;        /* 48px */
    --space-16: 4rem;        /* 64px */
    --space-20: 5rem;        /* 80px */
    --space-24: 6rem;        /* 96px */
    --space-32: 8rem;        /* 128px */
    --space-40: 10rem;       /* 160px */
    --space-48: 12rem;       /* 192px */
    --space-56: 14rem;       /* 224px */
    --space-64: 16rem;       /* 256px */

    /* Bordures */
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;

    /* Ombres modernes */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

    /* Ombres colorées */
    --shadow-green: 0 10px 15px -3px rgb(34 197 94 / 0.1), 0 4px 6px -4px rgb(34 197 94 / 0.1);
    --shadow-green-lg: 0 20px 25px -5px rgb(34 197 94 / 0.1), 0 8px 10px -6px rgb(34 197 94 / 0.1);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--agro-600) 0%, var(--agro-700) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--agro-500) 0%, var(--agro-600) 100%);
    --gradient-hero: linear-gradient(135deg, var(--agro-800) 0%, var(--agro-600) 50%, var(--agro-500) 100%);
    --gradient-card: linear-gradient(145deg, var(--white) 0%, var(--gray-50) 100%);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-800);
    background: linear-gradient(180deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--agro-400);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--agro-500);
}

/* Utilitaires */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Typographie */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    color: var(--gray-900);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-4);
}

a {
    color: var(--agro-600);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--agro-700);
}

/* Boutons modernes */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
    font-weight: 600;
    border-radius: var(--border-radius-lg);
    border: none;
    cursor: pointer;
    text-decoration: none;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.025em;
    backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-green);
}

.btn-primary:hover {
    background: var(--gradient-secondary);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-green-lg);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: var(--agro-700);
    border: 2px solid var(--agro-200);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--agro-600);
    color: var(--white);
    border-color: var(--agro-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-lg {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius-xl);
}

.btn-sm {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-sm);
}

/* Cartes modernes */
.card {
    background: var(--gradient-card);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all var(--transition-normal);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl);
}

.card:hover::before {
    transform: scaleX(1);
}

.card-content {
    padding: var(--spacing-6);
}

.card-header {
    padding: var(--spacing-6);
    padding-bottom: var(--spacing-4);
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
}

.card-description {
    color: var(--gray-600);
    margin-bottom: 0;
}

/* Grilles */
.grid {
    display: grid;
    gap: var(--spacing-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive */
@media (min-width: 640px) {
    .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .sm\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 768px) {
    .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

/* Sections modernes */
.section {
    padding: var(--spacing-20) 0;
    position: relative;
    overflow: hidden;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: -50%;
    width: 200%;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--agro-200), transparent);
}

.section-bg {
    background: linear-gradient(135deg, var(--agro-50) 0%, var(--surface-secondary) 100%);
    position: relative;
}

.section-bg::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(34, 197, 94, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.section-dark {
    background: linear-gradient(135deg, var(--agro-900) 0%, var(--agro-800) 100%);
    color: var(--white);
    position: relative;
}

.section-dark::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
    position: relative;
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    margin-bottom: var(--spacing-6);
    position: relative;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.section-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
    font-weight: 400;
}

.section-dark .section-title {
    background: linear-gradient(135deg, var(--white) 0%, var(--agro-100) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-dark .section-subtitle {
    color: var(--gray-200);
}

/* Animations sophistiquées */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1) rotate(0deg);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1) rotate(120deg);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9) rotate(240deg);
    }
    100% {
        transform: translate(0px, 0px) scale(1) rotate(360deg);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.animate-blob {
    animation: blob 8s infinite ease-in-out;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-out forwards;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out forwards;
}

.scale-in {
    animation: scaleIn 0.6s ease-out forwards;
}

/* Animation delays */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }

/* Barre de progression de scroll */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: var(--gradient-primary);
    z-index: 9999;
    transition: width 0.1s ease;
}

/* Effets de particules */
.particle {
    pointer-events: none;
    z-index: 1;
}

/* Animations supplémentaires */
@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--agro-400);
    }
    50% {
        box-shadow: 0 0 20px var(--agro-400), 0 0 30px var(--agro-400);
    }
}

/* Effets de hover avancés */
.card-advanced {
    position: relative;
    overflow: hidden;
}

.card-advanced::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s;
    opacity: 0;
}

.card-advanced:hover::before {
    animation: shimmer 1.5s ease-in-out;
    opacity: 1;
}

/* Boutons avec effet de vague */
.btn-wave {
    position: relative;
    overflow: hidden;
}

.btn-wave::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-wave:active::after {
    width: 300px;
    height: 300px;
}

/* Texte avec effet de gradient animé */
.text-gradient-animated {
    background: linear-gradient(-45deg, var(--agro-400), var(--agro-600), var(--agro-500), var(--agro-700));
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Effet de loading skeleton */
.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Effet de focus amélioré */
.focus-enhanced:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.3);
    border-color: var(--agro-500);
}

/* Transitions fluides pour tous les éléments interactifs */
a, button, input, textarea, select {
    transition: all var(--transition-normal);
}

/* Effet de survol pour les images */
.image-hover {
    overflow: hidden;
    border-radius: var(--border-radius-lg);
}

.image-hover img {
    transition: transform 0.5s ease;
}

.image-hover:hover img {
    transform: scale(1.1);
}

/* Utilitaires d'espacement */
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }
.mb-12 { margin-bottom: var(--spacing-12); }

.mt-4 { margin-top: var(--spacing-4); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }
.mt-12 { margin-top: var(--spacing-12); }

.py-16 { padding-top: var(--spacing-16); padding-bottom: var(--spacing-16); }
.py-24 { padding-top: var(--spacing-24); padding-bottom: var(--spacing-24); }

.px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }

.text-center { text-align: center; }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.rounded-lg { border-radius: var(--border-radius-lg); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.overflow-hidden { overflow: hidden; }

.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.object-cover { object-fit: cover; }

.relative { position: relative; }
.absolute { position: absolute; }

.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

.z-10 { z-index: 10; }
.z-50 { z-index: 50; }

/* Navigation moderne */
.navbar {
    position: sticky;
    top: 0;
    z-index: 50;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(34, 197, 94, 0.1);
    transition: all var(--transition-normal);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: var(--shadow-lg);
    border-bottom: 1px solid rgba(34, 197, 94, 0.2);
}

.navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 4rem;
}

.navbar-brand .logo {
    max-height: 3rem;
    width: auto;
    transition: transform var(--transition-normal);
}

.navbar-brand .logo:hover {
    transform: scale(1.05);
}

.desktop-nav {
    display: none;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-2);
}

.nav-link {
    padding: var(--spacing-3) var(--spacing-4);
    font-weight: 600;
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-normal);
    position: relative;
    color: var(--gray-700);
    letter-spacing: 0.025em;
}

.nav-link::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.nav-link:hover {
    color: var(--white);
    transform: translateY(-1px);
}

.nav-link:hover::before {
    opacity: 1;
}

.nav-link.active {
    color: var(--white);
    background: var(--gradient-primary);
    box-shadow: var(--shadow-green);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%) scaleX(0);
    width: 80%;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px;
    transition: transform var(--transition-normal);
}

.nav-link:hover::after {
    transform: translateX(-50%) scaleX(1);
}

.nav-link > span {
    position: relative;
    z-index: 1;
}

/* Menu mobile */
.mobile-menu-toggle {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 2rem;
    height: 2rem;
    background: none;
    border: none;
    cursor: pointer;
    position: relative;
}

.hamburger-line {
    width: 1.5rem;
    height: 2px;
    background-color: var(--agro-800);
    transition: all var(--transition-normal);
    margin: 2px 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-lg);
}

.mobile-nav.active {
    display: block;
    animation: slideDown 0.3s ease-out;
}

.mobile-nav-list {
    list-style: none;
    padding: var(--spacing-3) 0;
}

.mobile-nav-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-6);
    color: var(--gray-700);
    transition: all var(--transition-fast);
}

.mobile-nav-link:hover {
    background-color: var(--agro-50);
    color: var(--agro-700);
}

.mobile-nav-link.active {
    background-color: var(--agro-100);
    color: var(--agro-800);
    font-weight: 600;
}

.nav-arrow {
    width: 1rem;
    height: 1rem;
    transition: transform var(--transition-fast);
}

.mobile-nav-link:hover .nav-arrow {
    transform: translateX(4px);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (min-width: 768px) {
    .desktop-nav {
        display: block;
    }

    .mobile-menu-toggle {
        display: none;
    }

    .navbar-content {
        height: 4.5rem;
    }

    .navbar-brand .logo {
        max-height: 3.5rem;
    }
}

/* Hero Section moderne */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background-image: url('https://images.unsplash.com/photo-1461354464878-ad92f492a5a0?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(20, 83, 45, 0.9) 0%,
        rgba(21, 128, 61, 0.8) 30%,
        rgba(22, 163, 74, 0.7) 60%,
        rgba(34, 197, 94, 0.6) 100%);
    backdrop-filter: blur(1px);
}

.hero::after {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 30% 70%, rgba(34, 197, 94, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(22, 163, 74, 0.2) 0%, transparent 50%);
}

.hero-content {
    position: relative;
    z-index: 10;
    color: var(--white);
    max-width: 900px;
    animation: heroFadeIn 1.2s ease-out;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    margin-bottom: var(--spacing-6);
    line-height: 1.1;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-8);
    opacity: 0.95;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    font-weight: 400;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    animation: heroButtonsSlide 1.5s ease-out 0.3s both;
}

@keyframes heroFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes heroButtonsSlide {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.blob-shape {
    position: absolute;
    border-radius: 50%;
    mix-blend-mode: multiply;
    filter: blur(40px);
    opacity: 0.7;
}

.blob-1 {
    top: 30%;
    left: 20%;
    width: 16rem;
    height: 16rem;
    background-color: var(--agro-300);
    animation: blob 7s infinite alternate;
}

.blob-2 {
    bottom: 20%;
    right: 25%;
    width: 20rem;
    height: 20rem;
    background-color: var(--agro-400);
    animation: blob 7s infinite alternate;
    animation-delay: 2s;
}

.blob-3 {
    top: 15%;
    right: 15%;
    width: 18rem;
    height: 18rem;
    background-color: var(--agro-200);
    animation: blob 7s infinite alternate;
    animation-delay: 4s;
}

@media (min-width: 640px) {
    .hero-buttons {
        flex-direction: row;
    }
}

@media (min-width: 768px) {
    .hero-title {
        font-size: var(--font-size-5xl);
    }
}

/* Stats Cards modernes */
.stats-card {
    background: linear-gradient(145deg, var(--white) 0%, var(--agro-50) 100%);
    padding: var(--spacing-8);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid rgba(34, 197, 94, 0.1);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: var(--shadow-green-lg);
}

.stats-card:hover::before {
    transform: scaleX(1);
}

.stats-icon {
    width: 4rem;
    height: 4rem;
    margin: 0 auto var(--spacing-4);
    color: var(--white);
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-green);
    transition: all var(--transition-normal);
}

.stats-card:hover .stats-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-green-lg);
}

.stats-value {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-2);
    line-height: 1.2;
}

.stats-label {
    color: var(--gray-700);
    font-weight: 600;
    font-size: var(--font-size-base);
    letter-spacing: 0.025em;
}

/* Progress Bar */
.progress {
    width: 100%;
    height: 0.5rem;
    background-color: var(--gray-200);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--agro-500), var(--agro-600));
    border-radius: var(--border-radius);
    transition: width 1s ease-in-out;
}

/* Tabs */
.tabs {
    width: 100%;
}

.tabs-list {
    display: flex;
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    padding: var(--spacing-1);
    margin-bottom: var(--spacing-8);
}

.tabs-trigger {
    flex: 1;
    padding: var(--spacing-3) var(--spacing-4);
    background: none;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: all var(--transition-fast);
}

.tabs-trigger.active {
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
}

.tabs-content {
    display: none;
}

.tabs-content.active {
    display: block;
    animation: fadeInUp 0.3s ease-out;
}

/* Footer */
.footer {
    background-color: var(--agro-900);
    color: var(--white);
    padding: var(--spacing-12) 0 var(--spacing-6);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-12);
}

.footer-section {
    text-align: center;
}

.footer-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
    color: var(--white);
}

.footer-description {
    color: var(--gray-300);
    margin-bottom: var(--spacing-4);
    line-height: 1.6;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background-color: var(--agro-800);
    border-radius: 50%;
    color: var(--gray-300);
    transition: all var(--transition-fast);
}

.social-link:hover {
    background-color: var(--agro-600);
    color: var(--white);
    transform: translateY(-2px);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-3);
}

.footer-link {
    color: var(--gray-300);
    transition: color var(--transition-fast);
}

.footer-link:hover {
    color: var(--white);
}

.footer-contact {
    list-style: none;
}

.contact-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-3);
    color: var(--gray-300);
}

.contact-icon {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: var(--spacing-2);
    color: var(--agro-400);
    flex-shrink: 0;
}

.footer-bottom {
    padding-top: var(--spacing-6);
    border-top: 1px solid var(--gray-700);
    text-align: center;
}

.copyright {
    color: var(--gray-400);
    margin: 0;
}

@media (min-width: 768px) {
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-section {
        text-align: left;
    }

    .social-links {
        justify-content: flex-start;
    }

    .contact-item {
        justify-content: flex-start;
    }
}

@media (min-width: 1024px) {
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* ===== HERO SECTION MODERNE ===== */
.hero-modern {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--surface-accent) 0%, var(--primary-50) 50%, var(--secondary-50) 100%);
    padding: var(--space-20) 0;
}

.hero-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.decoration-dots {
    position: absolute;
    width: 120px;
    height: 120px;
    background-image: radial-gradient(circle, var(--primary-300) 2px, transparent 2px);
    background-size: 20px 20px;
    opacity: 0.3;
}

.decoration-dots-1 {
    top: 10%;
    left: 5%;
    animation: float 6s ease-in-out infinite;
}

.decoration-dots-2 {
    bottom: 15%;
    right: 8%;
    animation: float 8s ease-in-out infinite reverse;
}

.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--accent-orange), var(--accent-pink));
    opacity: 0.1;
}

.decoration-circle-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    right: 10%;
    animation: rotate 20s linear infinite;
}

.decoration-circle-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 10%;
    animation: rotate 15s linear infinite reverse;
}

.hero-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-content {
    max-width: 600px;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    background: var(--white);
    padding: var(--space-2) var(--space-4);
    border-radius: 50px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--space-6);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--secondary-700);
}

.badge-icon {
    font-size: 1.2em;
}

.hero-title-modern {
    font-family: var(--font-display);
    font-size: var(--text-5xl);
    font-weight: var(--font-bold);
    line-height: 1.1;
    margin-bottom: var(--space-6);
    color: var(--secondary-900);
}

.text-highlight {
    color: var(--primary-600);
    position: relative;
}

.text-gradient-modern {
    background: linear-gradient(135deg, var(--primary-600), var(--accent-orange));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description-modern {
    font-size: var(--text-lg);
    color: var(--secondary-600);
    line-height: 1.6;
    margin-bottom: var(--space-8);
}

.hero-actions-modern {
    margin-bottom: var(--space-12);
}

.btn-hero-primary {
    display: inline-flex;
    align-items: center;
    gap: var(--space-3);
    background: var(--primary-600);
    color: var(--white);
    padding: var(--space-4) var(--space-8);
    border-radius: 50px;
    font-weight: var(--font-semibold);
    font-size: var(--text-lg);
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.btn-hero-primary:hover {
    background: var(--primary-700);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(34, 197, 94, 0.4);
}

/* Statistiques circulaires */
.hero-stats-circle {
    display: flex;
    align-items: center;
}

.stats-circle-container {
    position: relative;
}

.circle-progress {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-500) 85%, var(--gray-200) 85%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.circle-progress::before {
    content: '';
    position: absolute;
    width: 90px;
    height: 90px;
    background: var(--white);
    border-radius: 50%;
}

.circle-inner {
    position: relative;
    z-index: 2;
    text-align: center;
}

.circle-number {
    display: block;
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--primary-600);
    line-height: 1;
}

.circle-label {
    display: block;
    font-size: var(--text-sm);
    color: var(--secondary-600);
    margin-top: var(--space-1);
}

/* Responsive utilities */
@media (max-width: 767px) {
    .container {
        padding: 0 var(--space-4);
    }

    .section {
        padding: var(--space-12) 0;
    }

    .hero-title {
        font-size: var(--text-3xl);
    }

    .hero-subtitle {
        font-size: var(--text-lg);
    }

    .section-title {
        font-size: var(--text-2xl);
    }

    .hero-grid {
        grid-template-columns: 1fr;
        gap: var(--space-12);
        text-align: center;
    }

    .hero-visual {
        order: -1;
    }

    .hero-title-modern {
        font-size: var(--text-4xl);
    }
}

/* ===== PARTIE VISUELLE HERO ===== */
.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image-container {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.main-image-wrapper {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hero-main-image {
    width: 100%;
    height: 600px;
    object-fit: cover;
    display: block;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
}

/* Cartes flottantes */
.floating-card {
    position: absolute;
    background: var(--white);
    border-radius: 16px;
    padding: var(--space-4);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: float 6s ease-in-out infinite;
    max-width: 280px;
}

.floating-card-1 {
    top: 10%;
    left: -20%;
    animation-delay: 0s;
}

.floating-card-2 {
    bottom: 10%;
    right: -20%;
    animation-delay: 3s;
}

.card-header {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-3);
}

.card-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.card-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-info h5 {
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    color: var(--secondary-900);
    margin: 0 0 var(--space-1) 0;
}

.card-badge {
    font-size: var(--text-xs);
    padding: var(--space-1) var(--space-2);
    border-radius: 6px;
    font-weight: var(--font-medium);
}

.card-badge-science {
    background: rgba(34, 197, 94, 0.1);
    color: var(--primary-700);
}

.card-badge-dev {
    background: rgba(59, 130, 246, 0.1);
    color: var(--accent-blue);
}

.card-title {
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    color: var(--secondary-900);
    margin: 0 0 var(--space-3) 0;
    line-height: 1.4;
}

.card-stats {
    display: flex;
    gap: var(--space-4);
    margin-bottom: var(--space-3);
}

.stat {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: var(--text-xs);
    color: var(--secondary-600);
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price {
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    color: var(--secondary-900);
}

.rating {
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.stars {
    color: #fbbf24;
    font-size: var(--text-sm);
}

.rating-text {
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    color: var(--secondary-700);
}

/* Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Responsive pour les cartes flottantes */
@media (max-width: 1024px) {
    .floating-card-1 {
        left: -10%;
        top: 5%;
    }

    .floating-card-2 {
        right: -10%;
        bottom: 5%;
    }
}

@media (max-width: 768px) {
    .floating-card {
        position: static;
        margin: var(--space-4) 0;
        max-width: none;
    }

    .hero-main-image {
        height: 400px;
    }
}
