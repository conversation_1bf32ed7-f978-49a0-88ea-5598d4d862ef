/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Couleurs principales */
    --agro-50: #f0f9f4;
    --agro-100: #dcf4e3;
    --agro-200: #bce8cc;
    --agro-300: #8dd5a8;
    --agro-400: #5bb97d;
    --agro-500: #369c5a;
    --agro-600: #2a7f47;
    --agro-700: #22653a;
    --agro-800: #1e5232;
    --agro-900: #1a432a;

    /* Couleurs système */
    --white: #ffffff;
    --black: #000000;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Typographie */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;

    /* Espacement */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    --spacing-24: 6rem;

    /* Bordures */
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;

    /* Ombres */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
}

/* Utilitaires */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Typographie */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    color: var(--gray-900);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-4);
}

a {
    color: var(--agro-600);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--agro-700);
}

/* Boutons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
    font-weight: 500;
    border-radius: var(--border-radius);
    border: none;
    cursor: pointer;
    text-decoration: none;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background-color: var(--agro-600);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--agro-700);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--white);
    color: var(--agro-600);
    border: 2px solid var(--agro-600);
}

.btn-secondary:hover {
    background-color: var(--agro-600);
    color: var(--white);
}

.btn-lg {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-lg);
}

/* Cartes */
.card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card-content {
    padding: var(--spacing-6);
}

.card-header {
    padding: var(--spacing-6);
    padding-bottom: var(--spacing-4);
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
}

.card-description {
    color: var(--gray-600);
    margin-bottom: 0;
}

/* Grilles */
.grid {
    display: grid;
    gap: var(--spacing-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive */
@media (min-width: 640px) {
    .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .sm\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 768px) {
    .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

/* Sections */
.section {
    padding: var(--spacing-16) 0;
}

.section-bg {
    background-color: var(--agro-50);
}

.section-dark {
    background-color: var(--agro-900);
    color: var(--white);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-12);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    position: relative;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.section-dark .section-subtitle {
    color: var(--gray-300);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes blob {
    0% { transform: translate(0px, 0px) scale(1); }
    33% { transform: translate(30px, -50px) scale(1.1); }
    66% { transform: translate(-20px, 20px) scale(0.9); }
    100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
    animation: blob 7s infinite alternate;
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Utilitaires d'espacement */
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }
.mb-12 { margin-bottom: var(--spacing-12); }

.mt-4 { margin-top: var(--spacing-4); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }
.mt-12 { margin-top: var(--spacing-12); }

.py-16 { padding-top: var(--spacing-16); padding-bottom: var(--spacing-16); }
.py-24 { padding-top: var(--spacing-24); padding-bottom: var(--spacing-24); }

.px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }

.text-center { text-align: center; }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.rounded-lg { border-radius: var(--border-radius-lg); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.overflow-hidden { overflow: hidden; }

.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.object-cover { object-fit: cover; }

.relative { position: relative; }
.absolute { position: absolute; }

.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

.z-10 { z-index: 10; }
.z-50 { z-index: 50; }

/* Navigation */
.navbar {
    position: sticky;
    top: 0;
    z-index: 50;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    box-shadow: var(--shadow-lg);
    background-color: rgba(255, 255, 255, 0.98);
}

.navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 4rem;
}

.navbar-brand .logo {
    max-height: 3rem;
    width: auto;
    transition: transform var(--transition-normal);
}

.navbar-brand .logo:hover {
    transform: scale(1.05);
}

.desktop-nav {
    display: none;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-2);
}

.nav-link {
    padding: var(--spacing-2) var(--spacing-3);
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    background-color: var(--agro-50);
    color: var(--agro-700);
}

.nav-link.active {
    background-color: var(--agro-100);
    color: var(--agro-800);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%) scaleX(0);
    width: 50%;
    height: 2px;
    background-color: var(--agro-500);
    transition: transform var(--transition-normal);
}

.nav-link:hover::after {
    transform: translateX(-50%) scaleX(1);
}

/* Menu mobile */
.mobile-menu-toggle {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 2rem;
    height: 2rem;
    background: none;
    border: none;
    cursor: pointer;
    position: relative;
}

.hamburger-line {
    width: 1.5rem;
    height: 2px;
    background-color: var(--agro-800);
    transition: all var(--transition-normal);
    margin: 2px 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-lg);
}

.mobile-nav.active {
    display: block;
    animation: slideDown 0.3s ease-out;
}

.mobile-nav-list {
    list-style: none;
    padding: var(--spacing-3) 0;
}

.mobile-nav-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-6);
    color: var(--gray-700);
    transition: all var(--transition-fast);
}

.mobile-nav-link:hover {
    background-color: var(--agro-50);
    color: var(--agro-700);
}

.mobile-nav-link.active {
    background-color: var(--agro-100);
    color: var(--agro-800);
    font-weight: 600;
}

.nav-arrow {
    width: 1rem;
    height: 1rem;
    transition: transform var(--transition-fast);
}

.mobile-nav-link:hover .nav-arrow {
    transform: translateX(4px);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (min-width: 768px) {
    .desktop-nav {
        display: block;
    }

    .mobile-menu-toggle {
        display: none;
    }

    .navbar-content {
        height: 4.5rem;
    }

    .navbar-brand .logo {
        max-height: 3.5rem;
    }
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background-image: url('https://images.unsplash.com/photo-1461354464878-ad92f492a5a0?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
    background-size: cover;
    background-position: center;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(42, 127, 71, 0.8) 0%, rgba(34, 101, 58, 0.7) 50%, rgba(54, 156, 90, 0.6) 100%);
}

.hero-content {
    position: relative;
    z-index: 10;
    color: var(--white);
    max-width: 800px;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-6);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-8);
    opacity: 0.95;
}

.hero-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.blob-shape {
    position: absolute;
    border-radius: 50%;
    mix-blend-mode: multiply;
    filter: blur(40px);
    opacity: 0.7;
}

.blob-1 {
    top: 30%;
    left: 20%;
    width: 16rem;
    height: 16rem;
    background-color: var(--agro-300);
    animation: blob 7s infinite alternate;
}

.blob-2 {
    bottom: 20%;
    right: 25%;
    width: 20rem;
    height: 20rem;
    background-color: var(--agro-400);
    animation: blob 7s infinite alternate;
    animation-delay: 2s;
}

.blob-3 {
    top: 15%;
    right: 15%;
    width: 18rem;
    height: 18rem;
    background-color: var(--agro-200);
    animation: blob 7s infinite alternate;
    animation-delay: 4s;
}

@media (min-width: 640px) {
    .hero-buttons {
        flex-direction: row;
    }
}

@media (min-width: 768px) {
    .hero-title {
        font-size: var(--font-size-5xl);
    }
}

/* Stats Cards */
.stats-card {
    background-color: var(--white);
    padding: var(--spacing-6);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    text-align: center;
    transition: all var(--transition-normal);
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stats-icon {
    width: 3rem;
    height: 3rem;
    margin: 0 auto var(--spacing-4);
    color: var(--agro-600);
    background-color: var(--agro-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stats-value {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--agro-800);
    margin-bottom: var(--spacing-2);
}

.stats-label {
    color: var(--gray-600);
    font-weight: 500;
}

/* Progress Bar */
.progress {
    width: 100%;
    height: 0.5rem;
    background-color: var(--gray-200);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--agro-500), var(--agro-600));
    border-radius: var(--border-radius);
    transition: width 1s ease-in-out;
}

/* Tabs */
.tabs {
    width: 100%;
}

.tabs-list {
    display: flex;
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    padding: var(--spacing-1);
    margin-bottom: var(--spacing-8);
}

.tabs-trigger {
    flex: 1;
    padding: var(--spacing-3) var(--spacing-4);
    background: none;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: all var(--transition-fast);
}

.tabs-trigger.active {
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
}

.tabs-content {
    display: none;
}

.tabs-content.active {
    display: block;
    animation: fadeInUp 0.3s ease-out;
}

/* Footer */
.footer {
    background-color: var(--agro-900);
    color: var(--white);
    padding: var(--spacing-12) 0 var(--spacing-6);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-12);
}

.footer-section {
    text-align: center;
}

.footer-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
    color: var(--white);
}

.footer-description {
    color: var(--gray-300);
    margin-bottom: var(--spacing-4);
    line-height: 1.6;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background-color: var(--agro-800);
    border-radius: 50%;
    color: var(--gray-300);
    transition: all var(--transition-fast);
}

.social-link:hover {
    background-color: var(--agro-600);
    color: var(--white);
    transform: translateY(-2px);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-3);
}

.footer-link {
    color: var(--gray-300);
    transition: color var(--transition-fast);
}

.footer-link:hover {
    color: var(--white);
}

.footer-contact {
    list-style: none;
}

.contact-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-3);
    color: var(--gray-300);
}

.contact-icon {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: var(--spacing-2);
    color: var(--agro-400);
    flex-shrink: 0;
}

.footer-bottom {
    padding-top: var(--spacing-6);
    border-top: 1px solid var(--gray-700);
    text-align: center;
}

.copyright {
    color: var(--gray-400);
    margin: 0;
}

@media (min-width: 768px) {
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-section {
        text-align: left;
    }

    .social-links {
        justify-content: flex-start;
    }

    .contact-item {
        justify-content: flex-start;
    }
}

@media (min-width: 1024px) {
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Responsive utilities */
@media (max-width: 767px) {
    .container {
        padding: 0 var(--spacing-4);
    }

    .section {
        padding: var(--spacing-12) 0;
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .section-title {
        font-size: var(--font-size-2xl);
    }
}
