
import Hero from "@/components/home/<USER>";
import StatsCard from "@/components/home/<USER>";
import SectionHeader from "@/components/ui/section-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Link } from "react-router-dom";
import { BookOpen, Users, Sprout, Award, Calendar, ArrowRight } from "lucide-react";

const Index = () => {
  const stats = [
    { 
      icon: <BookOpen size={40} />, 
      value: 12, 
      label: "Formations dispensées" 
    },
    { 
      icon: <Users size={40} />, 
      value: "500+", 
      label: "Stagiaires inscrits" 
    },
    { 
      icon: <Sprout size={40} />, 
      value: 24, 
      label: "Projets réalisés" 
    },
    { 
      icon: <Award size={40} />, 
      value: 15, 
      label: "Partenariats" 
    },
  ];

  const latestNews = [
    {
      id: 1,
      title: "Journée portes ouvertes",
      date: "15 Avril 2025",
      description: "Venez découvrir nos installations et formations lors de notre journée portes ouvertes annuelle.",
      image: "https://images.unsplash.com/photo-1523348837708-15d4a09cfac2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    },
    {
      id: 2,
      title: "Nouveau partenariat avec OCP",
      date: "3 Mars 2025",
      description: "Un nouveau partenariat stratégique a été signé avec l'OCP pour soutenir nos programmes de formation.",
      image: "https://images.unsplash.com/photo-1560493676-04071c5f467b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    },
    {
      id: 3,
      title: "Lancement du programme d'agriculture durable",
      date: "20 Février 2025",
      description: "Notre pôle lance un nouveau programme dédié aux techniques d'agriculture durable et écologique.",
      image: "https://images.unsplash.com/photo-1500651230702-0e2d8a49d4ad?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    }
  ];

  return (
    <div>
      <Hero />
      
      <section className="py-16 md:py-24 px-6 container mx-auto">
        <SectionHeader 
          title="La Cité des Métiers et des Compétences" 
          subtitle="Une institution d'excellence pour la formation professionnelle au Maroc"
          center
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div>
            <p className="text-lg mb-6">
              La Cité des Métiers et des Compétences (CMC) est un établissement public dédié à la formation professionnelle, offrant des programmes qui répondent aux besoins du marché du travail marocain.
            </p>
            <p className="text-lg mb-6">
              Notre mission est de former les compétences de demain dans divers secteurs stratégiques, dont l'agriculture, secteur vital pour l'économie marocaine.
            </p>
            <Button asChild>
              <Link to="/presentation">En savoir plus sur la CMC</Link>
            </Button>
          </div>
          <div className="rounded-lg overflow-hidden shadow-lg">
            <img 
              src="https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" 
              alt="Cité des Métiers et des Compétences" 
              className="w-full h-full object-cover"
            />
          </div>
        </div>
        
        <SectionHeader 
          title="Le Pôle Agriculture" 
          subtitle="Formation d'excellence dans le domaine agricole"
          className="mt-16"
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div className="order-2 md:order-1 rounded-lg overflow-hidden shadow-lg">
            <img 
              src="https://images.unsplash.com/photo-1500651230702-0e2d8a49d4ad?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" 
              alt="Pôle Agriculture" 
              className="w-full h-full object-cover"
            />
          </div>
          <div className="order-1 md:order-2">
            <p className="text-lg mb-6">
              Le Pôle Agriculture de la CMC est dédié à la formation des futures générations d'experts agricoles, capables de répondre aux défis du secteur et de contribuer au développement durable du pays.
            </p>
            <p className="text-lg mb-6">
              Nos formations couvrent divers aspects de l'agriculture moderne : techniques de culture, élevage, gestion d'exploitation, et agriculture durable.
            </p>
            <Button asChild>
              <Link to="/formations">Découvrir nos formations</Link>
            </Button>
          </div>
        </div>
      </section>
      
      <section className="py-16 bg-agro-50">
        <div className="container mx-auto px-6">
          <SectionHeader 
            title="Le Pôle Agriculture en chiffres" 
            subtitle="Des résultats concrets dans la formation agricole"
            center
          />
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <StatsCard 
                key={index}
                icon={stat.icon}
                value={stat.value}
                label={stat.label}
              />
            ))}
          </div>
        </div>
      </section>
      
      <section className="py-16 md:py-24 container mx-auto px-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <SectionHeader 
            title="Actualités et événements" 
            subtitle="Restez informé des dernières nouvelles du Pôle Agriculture"
            className="mb-0"
          />
          <Button asChild variant="outline" className="mt-4 md:mt-0">
            <Link to="/actualites" className="flex items-center">
              Toutes les actualités <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {latestNews.map((news) => (
            <Card key={news.id} className="overflow-hidden card-hover">
              <div className="h-48 overflow-hidden">
                <img 
                  src={news.image} 
                  alt={news.title} 
                  className="w-full h-full object-cover"
                />
              </div>
              <CardHeader>
                <div className="flex items-center text-sm text-muted-foreground mb-2">
                  <Calendar className="mr-2 h-4 w-4" />
                  {news.date}
                </div>
                <CardTitle>{news.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  {news.description}
                </CardDescription>
                <Button asChild variant="link" className="px-0 mt-4">
                  <Link to="/actualites" className="flex items-center">
                    Lire la suite <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>
      
      <section className="py-16 bg-agro-900 text-white">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Prêt à rejoindre nos formations ?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto">
            Inscrivez-vous dès maintenant pour développer vos compétences dans le secteur agricole et préparer votre avenir professionnel.
          </p>
          <Button asChild size="lg" className="bg-white text-agro-800 hover:bg-gray-100">
            <Link to="/formations">S'inscrire aux formations</Link>
          </Button>
        </div>
      </section>
    </div>
  );
};

export default Index;
