
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 120 29% 97%;
    --foreground: 120 7% 15%;

    --card: 0 0% 100%;
    --card-foreground: 120 7% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 120 7% 15%;

    --primary: 134 30% 40%;
    --primary-foreground: 120 29% 97%;

    --secondary: 80 65% 90%;
    --secondary-foreground: 120 7% 15%;

    --muted: 120 7% 95%;
    --muted-foreground: 120 7% 40%;

    --accent: 72 76% 90%;
    --accent-foreground: 120 7% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 120 29% 97%;

    --border: 120 7% 90%;
    --input: 120 7% 90%;
    --ring: 134 30% 40%;

    --radius: 0.5rem;

    --sidebar-background: 120 29% 97%;
    --sidebar-foreground: 120 7% 15%;
    --sidebar-primary: 134 30% 40%;
    --sidebar-primary-foreground: 120 29% 97%;
    --sidebar-accent: 120 7% 95%;
    --sidebar-accent-foreground: 120 7% 15%;
    --sidebar-border: 120 7% 90%;
    --sidebar-ring: 134 30% 40%;
  }

  .dark {
    --background: 120 7% 10%;
    --foreground: 120 29% 97%;

    --card: 120 7% 12%;
    --card-foreground: 120 29% 97%;

    --popover: 120 7% 12%;
    --popover-foreground: 120 29% 97%;

    --primary: 134 30% 40%;
    --primary-foreground: 120 29% 97%;

    --secondary: 120 7% 20%;
    --secondary-foreground: 120 29% 97%;

    --muted: 120 7% 18%;
    --muted-foreground: 120 7% 65%;

    --accent: 120 7% 20%;
    --accent-foreground: 120 29% 97%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 120 29% 97%;

    --border: 120 7% 18%;
    --input: 120 7% 18%;
    --ring: 134 30% 40%;

    --sidebar-background: 120 7% 10%;
    --sidebar-foreground: 120 29% 97%;
    --sidebar-primary: 134 30% 40%;
    --sidebar-primary-foreground: 120 29% 97%;
    --sidebar-accent: 120 7% 18%;
    --sidebar-accent-foreground: 120 29% 97%;
    --sidebar-border: 120 7% 18%;
    --sidebar-ring: 134 30% 40%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }

  .hero-section {
    background-image: url('https://images.unsplash.com/photo-1461354464878-ad92f492a5a0?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
    background-size: cover;
    background-position: center;
    position: relative;
    @apply overflow-hidden;
  }

  .hero-section::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-agro-800/80 via-agro-700/70 to-agro-600/60;
  }
  
  /* Animated blob shapes */
  .blob-shape {
    @apply absolute rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob;
  }
  
  .blob-shape-1 {
    @apply bg-agro-300 top-[30%] left-[20%] w-64 h-64;
    animation-delay: 0s;
  }
  
  .blob-shape-2 {
    @apply bg-agro-400 bottom-[20%] right-[25%] w-80 h-80;
    animation-delay: 2s;
  }
  
  .blob-shape-3 {
    @apply bg-agro-200 top-[15%] right-[15%] w-72 h-72;
    animation-delay: 4s;
  }
}

@layer components {
  .nav-link {
    @apply px-3 py-2 text-base font-medium rounded-md transition-colors hover:bg-agro-100 hover:text-agro-800;
  }

  .nav-link.active {
    @apply bg-agro-100 text-agro-800;
  }

  /* Modern card hover effect */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-2 relative bg-white overflow-hidden;
  }
  
  .card-hover::after {
    content: '';
    @apply absolute inset-0 top-0 left-0 w-full h-full bg-gradient-to-br from-agro-200/30 to-agro-400/20 opacity-0 transition-opacity duration-300;
  }
  
  .card-hover:hover::after {
    @apply opacity-100;
  }

  .stats-card {
    @apply bg-white shadow-md rounded-lg p-6 flex flex-col items-center justify-center card-hover;
  }
  
  .section-heading {
    @apply text-3xl md:text-4xl font-bold mb-6 text-agro-800 relative inline-block;
  }
  
  .section-heading::after {
    content: '';
    @apply absolute bottom-0 left-0 w-1/3 h-1 bg-gradient-to-r from-agro-400 to-agro-600 rounded;
  }
  
  /* Button hover effect */
  .btn-hover-slide {
    @apply relative overflow-hidden;
  }
  
  .btn-hover-slide::after {
    content: '';
    @apply absolute top-0 left-0 w-full h-full bg-white/20 transform -translate-x-full transition-transform duration-300;
  }
  
  .btn-hover-slide:hover::after {
    @apply translate-x-0;
  }
  
  /* Animated lists */
  .animate-list > * {
    @apply opacity-0 translate-y-4 transition-all duration-300;
    animation: fadeInUp 0.5s forwards;
  }
  
  .animate-list > *:nth-child(1) { animation-delay: 0.1s; }
  .animate-list > *:nth-child(2) { animation-delay: 0.2s; }
  .animate-list > *:nth-child(3) { animation-delay: 0.3s; }
  .animate-list > *:nth-child(4) { animation-delay: 0.4s; }
  .animate-list > *:nth-child(5) { animation-delay: 0.5s; }
  .animate-list > *:nth-child(6) { animation-delay: 0.6s; }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@layer utilities {
  .animate-blob {
    animation: blob 7s infinite alternate;
  }
}
