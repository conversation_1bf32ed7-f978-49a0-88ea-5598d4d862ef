<?php
require_once 'includes/config.php';

$pageTitle = 'Démonstration des Effets Visuels';
$pageDescription = 'Page de démonstration des nouveaux effets visuels et animations du site CMC Agriculture.';

include 'includes/header.php';
?>

<!-- Barre de progression de scroll -->
<div class="scroll-progress"></div>

<!-- Hero Section avec effets -->
<section class="hero animated-gradient">
    <div class="floating-particles">
        <!-- Les particules seront générées par JavaScript -->
    </div>
    
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title neon-text">
                Démonstration des Effets
            </h1>
            <p class="hero-subtitle typing-effect">
                Découvrez les nouveaux effets visuels du site CMC Agriculture
            </p>
            <div class="hero-buttons">
                <a href="#effects" class="btn btn-primary btn-lg btn-morph pulse-glow">
                    Voir les effets
                    <i data-lucide="arrow-down" style="margin-left: 8px; width: 20px; height: 20px;"></i>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Section Cartes 3D -->
<section class="section wave-section" id="effects">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title text-gradient-animated">Cartes avec Effets 3D</h2>
            <p class="section-subtitle reveal">
                Survolez les cartes pour voir les effets de transformation 3D
            </p>
        </div>
        
        <div class="grid md:grid-cols-3">
            <div class="card card-3d card-advanced glass reveal">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background: var(--gradient-primary); padding: var(--spacing-3); border-radius: 50%; color: var(--white);">
                            <i data-lucide="zap" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Effet Glassmorphism</h3>
                    <p class="card-description">
                        Carte avec effet de verre dépoli et transformation 3D au survol.
                    </p>
                </div>
            </div>
            
            <div class="card card-3d animated-border reveal delay-200">
                <div class="card-content" style="text-align: center;">
                    <div style="margin-bottom: var(--spacing-4); display: flex; justify-content: center;">
                        <div style="background: var(--gradient-primary); padding: var(--spacing-3); border-radius: 50%; color: var(--white);">
                            <i data-lucide="sparkles" style="width: 2rem; height: 2rem;"></i>
                        </div>
                    </div>
                    <h3 class="card-title">Bordure Animée</h3>
                    <p class="card-description">
                        Carte avec bordure en rotation continue et effet 3D.
                    </p>
                </div>
            </div>
            
            <div class="card card-3d reveal delay-400">
                <div class="image-mask zoom-hover" style="height: 12rem; overflow: hidden;">
                    <img src="https://images.unsplash.com/photo-1500651230702-0e2d8a49d4ad?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" 
                         alt="Agriculture" 
                         class="w-full h-full object-cover">
                </div>
                <div class="card-content">
                    <h3 class="card-title">Image avec Masque</h3>
                    <p class="card-description">
                        Image avec effet de masque lumineux et zoom au survol.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section Boutons avec Effets -->
<section class="section section-bg">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Boutons Interactifs</h2>
            <p class="section-subtitle">
                Différents styles de boutons avec animations avancées
            </p>
        </div>
        
        <div style="display: flex; flex-wrap: wrap; gap: var(--spacing-4); justify-content: center;">
            <button class="btn btn-primary btn-wave">
                Effet de Vague
            </button>
            
            <button class="btn btn-secondary btn-morph">
                Morphing
            </button>
            
            <button class="btn btn-primary pulse-glow">
                Pulsation Lumineuse
            </button>
            
            <button class="btn btn-secondary animated-border">
                Bordure Animée
            </button>
        </div>
    </div>
</section>

<!-- Section Texte avec Effets -->
<section class="section section-dark">
    <div class="container text-center">
        <h2 class="neon-text" style="font-size: var(--font-size-4xl); margin-bottom: var(--spacing-6);">
            Texte Néon Clignotant
        </h2>
        
        <p class="text-gradient-animated" style="font-size: var(--font-size-xl); margin-bottom: var(--spacing-8);">
            Texte avec dégradé animé en continu
        </p>
        
        <div class="typing-effect" style="font-size: var(--font-size-lg); margin: 0 auto; max-width: 600px;">
            Ce texte apparaît avec un effet de machine à écrire...
        </div>
    </div>
</section>

<!-- Section Statistiques Animées -->
<section class="section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Statistiques Animées</h2>
            <p class="section-subtitle">
                Cartes de statistiques avec animations et effets visuels
            </p>
        </div>
        
        <div class="grid sm:grid-cols-2 lg:grid-cols-4">
            <div class="stats-card card-3d pulse-glow animate-float">
                <div class="stats-icon">
                    <i data-lucide="trending-up"></i>
                </div>
                <div class="stats-value text-gradient-animated">95%</div>
                <div class="stats-label">Taux de Réussite</div>
            </div>
            
            <div class="stats-card glass animate-float delay-200">
                <div class="stats-icon">
                    <i data-lucide="users"></i>
                </div>
                <div class="stats-value neon-text">1,250</div>
                <div class="stats-label">Étudiants Formés</div>
            </div>
            
            <div class="stats-card animated-border animate-float delay-300">
                <div class="stats-icon">
                    <i data-lucide="award"></i>
                </div>
                <div class="stats-value">25</div>
                <div class="stats-label">Certifications</div>
            </div>
            
            <div class="stats-card card-3d animate-float delay-400">
                <div class="stats-icon">
                    <i data-lucide="globe"></i>
                </div>
                <div class="stats-value text-gradient-animated">50+</div>
                <div class="stats-label">Partenaires</div>
            </div>
        </div>
    </div>
</section>

<!-- Section Navigation avec Indicateurs -->
<section class="section section-bg">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Navigation Animée</h2>
            <p class="section-subtitle">
                Éléments de navigation avec indicateurs de slide
            </p>
        </div>
        
        <div style="display: flex; justify-content: center; gap: var(--spacing-4); flex-wrap: wrap;">
            <a href="#" class="slide-indicator" style="padding: var(--spacing-3) var(--spacing-6); color: var(--agro-700); font-weight: 600;">
                Accueil
            </a>
            <a href="#" class="slide-indicator active" style="padding: var(--spacing-3) var(--spacing-6); color: var(--agro-700); font-weight: 600;">
                Formations
            </a>
            <a href="#" class="slide-indicator" style="padding: var(--spacing-3) var(--spacing-6); color: var(--agro-700); font-weight: 600;">
                Actualités
            </a>
            <a href="#" class="slide-indicator" style="padding: var(--spacing-3) var(--spacing-6); color: var(--agro-700); font-weight: 600;">
                Contact
            </a>
        </div>
    </div>
</section>

<!-- Section Loader -->
<section class="section">
    <div class="container text-center">
        <div class="section-header">
            <h2 class="section-title">Loader Moderne</h2>
            <p class="section-subtitle">
                Indicateur de chargement avec animation fluide
            </p>
        </div>
        
        <div class="modern-loader" style="margin: 0 auto;"></div>
    </div>
</section>

<script>
// Initialiser les effets de révélation
document.addEventListener('DOMContentLoaded', function() {
    // Observer pour les éléments à révéler
    const revealElements = document.querySelectorAll('.reveal');
    const revealObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
            }
        });
    }, { threshold: 0.1 });
    
    revealElements.forEach(element => {
        revealObserver.observe(element);
    });
    
    // Créer des particules flottantes
    const particlesContainer = document.querySelector('.floating-particles');
    if (particlesContainer) {
        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                width: ${Math.random() * 4 + 2}px;
                height: ${Math.random() * 4 + 2}px;
                left: ${Math.random() * 100}%;
                animation-delay: ${Math.random() * 6}s;
                animation-duration: ${6 + Math.random() * 4}s;
            `;
            particlesContainer.appendChild(particle);
        }
    }
});
</script>

<?php include 'includes/footer.php'; ?>
