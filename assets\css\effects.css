/* Effets visuels avancés pour le site CMC Agriculture */

/* Effet de glassmorphism */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Effet néon pour les titres */
.neon-text {
    color: var(--agro-400);
    text-shadow: 
        0 0 5px var(--agro-400),
        0 0 10px var(--agro-400),
        0 0 15px var(--agro-400),
        0 0 20px var(--agro-400);
    animation: neonFlicker 2s infinite alternate;
}

@keyframes neonFlicker {
    0%, 100% {
        text-shadow: 
            0 0 5px var(--agro-400),
            0 0 10px var(--agro-400),
            0 0 15px var(--agro-400),
            0 0 20px var(--agro-400);
    }
    50% {
        text-shadow: 
            0 0 2px var(--agro-400),
            0 0 5px var(--agro-400),
            0 0 8px var(--agro-400),
            0 0 12px var(--agro-400);
    }
}

/* Effet de morphing pour les boutons */
.btn-morph {
    position: relative;
    overflow: hidden;
    border-radius: 50px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.320, 1);
}

.btn-morph:hover {
    border-radius: 10px;
    transform: scale(1.05);
}

.btn-morph::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.btn-morph:hover::before {
    left: 100%;
}

/* Effet de rotation 3D pour les cartes */
.card-3d {
    perspective: 1000px;
    transform-style: preserve-3d;
}

.card-3d:hover {
    transform: rotateY(10deg) rotateX(5deg);
}

/* Effet de vague pour les sections */
.wave-section {
    position: relative;
    overflow: hidden;
}

.wave-section::before {
    content: '';
    position: absolute;
    top: -50px;
    left: 0;
    width: 100%;
    height: 100px;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z' fill='%2322c55e'%3E%3C/path%3E%3C/svg%3E") no-repeat;
    background-size: cover;
    opacity: 0.1;
}

/* Effet de particules flottantes */
.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.particle {
    position: absolute;
    background: var(--agro-400);
    border-radius: 50%;
    opacity: 0.6;
    animation: floatParticle 6s infinite ease-in-out;
}

@keyframes floatParticle {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

/* Effet de typing pour le texte */
.typing-effect {
    overflow: hidden;
    border-right: 3px solid var(--agro-500);
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: var(--agro-500); }
}

/* Effet de révélation progressive */
.reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease;
}

.reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Effet de masque pour les images */
.image-mask {
    position: relative;
    overflow: hidden;
}

.image-mask::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
    transition: left 0.8s;
    z-index: 1;
}

.image-mask:hover::before {
    left: 100%;
}

/* Effet de pulsation pour les éléments importants */
.pulse-glow {
    animation: pulseGlow 2s infinite;
}

@keyframes pulseGlow {
    0% {
        box-shadow: 0 0 5px var(--agro-400);
    }
    50% {
        box-shadow: 0 0 20px var(--agro-400), 0 0 30px var(--agro-400);
    }
    100% {
        box-shadow: 0 0 5px var(--agro-400);
    }
}

/* Effet de dégradé animé pour les arrière-plans */
.animated-gradient {
    background: linear-gradient(-45deg, var(--agro-400), var(--agro-600), var(--agro-500), var(--agro-700));
    background-size: 400% 400%;
    animation: gradientAnimation 15s ease infinite;
}

@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Effet de zoom sur les images au survol */
.zoom-hover {
    overflow: hidden;
    transition: transform 0.3s ease;
}

.zoom-hover img {
    transition: transform 0.5s ease;
}

.zoom-hover:hover img {
    transform: scale(1.1);
}

/* Effet de bordure animée */
.animated-border {
    position: relative;
    border: 2px solid transparent;
    background: linear-gradient(var(--white), var(--white)) padding-box,
                linear-gradient(45deg, var(--agro-400), var(--agro-600)) border-box;
}

.animated-border::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(45deg, var(--agro-400), var(--agro-600));
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    animation: borderRotate 3s linear infinite;
}

@keyframes borderRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Effet de loading moderne */
.modern-loader {
    width: 40px;
    height: 40px;
    border: 3px solid var(--agro-200);
    border-top: 3px solid var(--agro-500);
    border-radius: 50%;
    animation: modernSpin 1s linear infinite;
}

@keyframes modernSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Effet de slide pour les éléments de navigation */
.slide-indicator {
    position: relative;
    overflow: hidden;
}

.slide-indicator::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.slide-indicator:hover::after,
.slide-indicator.active::after {
    width: 100%;
}

/* Effet de parallax pour les sections */
.parallax-element {
    transform: translateZ(0);
    will-change: transform;
}

/* Responsive pour les effets */
@media (max-width: 768px) {
    .card-3d:hover {
        transform: none;
    }
    
    .neon-text {
        animation: none;
        text-shadow: 0 0 10px var(--agro-400);
    }
    
    .floating-particles {
        display: none;
    }
}
