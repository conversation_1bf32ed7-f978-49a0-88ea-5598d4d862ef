<?php
if (!defined('SITE_NAME')) {
    require_once 'config.php';
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <meta name="description" content="<?php echo isset($pageDescription) ? $pageDescription : SITE_DESCRIPTION; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo asset('images/favicon.ico'); ?>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo asset('css/style.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('css/effects.css'); ?>">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>
<body>
    <header class="navbar" id="navbar">
        <div class="container">
            <div class="navbar-content">
                <!-- Logo -->
                <div class="navbar-brand">
                    <a href="<?php echo url('index.php'); ?>" class="logo-link">
                        <img src="<?php echo asset('images/logo.svg'); ?>" alt="Logo CMC" class="logo">
                    </a>
                </div>

                <!-- Navigation Desktop -->
                <nav class="navbar-nav desktop-nav">
                    <ul class="nav-list">
                        <?php foreach ($navigation as $item): ?>
                            <li class="nav-item">
                                <a href="<?php echo url($item['url']); ?>" 
                                   class="nav-link <?php echo isCurrentPage($item['page']) ? 'active' : ''; ?>">
                                    <?php echo $item['name']; ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </nav>

                <!-- Menu Mobile Toggle -->
                <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Toggle navigation menu">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
            </div>

            <!-- Navigation Mobile -->
            <nav class="mobile-nav" id="mobileNav">
                <ul class="mobile-nav-list">
                    <?php foreach ($navigation as $item): ?>
                        <li class="mobile-nav-item">
                            <a href="<?php echo url($item['url']); ?>" 
                               class="mobile-nav-link <?php echo isCurrentPage($item['page']) ? 'active' : ''; ?>">
                                <?php echo $item['name']; ?>
                                <i data-lucide="chevron-right" class="nav-arrow"></i>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
