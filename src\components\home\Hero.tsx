
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, Leaf, BookOpen, Users } from "lucide-react";

const Hero = () => {
  return (
    <section className="hero-section relative">
      {/* Animated blob shapes */}
      <div className="blob-shape blob-shape-1"></div>
      <div className="blob-shape blob-shape-2"></div>
      <div className="blob-shape blob-shape-3"></div>
      
      <div className="relative py-28 md:py-36 lg:py-44 px-6 container mx-auto">
        <div className="max-w-3xl text-white">
          <div className="space-y-6">
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-white/20 backdrop-blur-sm text-white text-sm font-medium">
              <span className="flex h-3 w-3 mr-2">
                <span className="animate-ping absolute inline-flex h-3 w-3 rounded-full bg-green-400 opacity-75"></span>
                <span className="relative inline-flex rounded-full h-3 w-3 bg-green-500"></span>
              </span>
              Formation d'excellence en Agriculture
            </div>
            
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold animate-fade-in tracking-tight">
              <span className="bg-clip-text bg-gradient-to-r from-white to-white/90">Pôle Agriculture</span>
            </h1>
            
            <h2 className="text-2xl md:text-3xl font-light animate-fade-in">
              Cité des Métiers et des Compétences
            </h2>
            
            <p className="text-lg md:text-xl animate-fade-in leading-relaxed">
              Formez-vous aux métiers d'avenir dans le secteur agricole avec des programmes innovants et adaptés aux besoins du marché marocain.
            </p>
            
            <div className="flex flex-wrap gap-4 animate-fade-in pt-2">
              <Button asChild size="lg" className="bg-white text-agro-800 hover:bg-gray-100 btn-hover-slide group">
                <Link to="/formations">
                  Découvrir nos formations
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="text-emerald-500 border-emerald-500 hover:bg-emerald-500/20 hover:text-emerald-400">
                <Link to="/presentation">En savoir plus</Link>
              </Button>
            </div>
          </div>
        </div>
        
        {/* Feature cards */}
        <div className="mt-16 lg:mt-0 lg:absolute lg:bottom-0 lg:right-0 lg:transform lg:translate-y-1/2 max-w-4xl z-10 px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/90 backdrop-blur-md rounded-2xl md:rounded-l-2xl md:rounded-r-none p-6 shadow-xl flex flex-col items-center text-center">
              <div className="bg-agro-100 p-3 rounded-full mb-3">
                <Leaf className="h-6 w-6 text-agro-600" />
              </div>
              <h3 className="font-medium text-agro-800">Agriculture durable</h3>
              <p className="text-sm text-gray-600 mt-2">Pratiques respectueuses de l'environnement</p>
            </div>
            <div className="bg-white/90 backdrop-blur-md p-6 shadow-xl flex flex-col items-center text-center">
              <div className="bg-agro-100 p-3 rounded-full mb-3">
                <BookOpen className="h-6 w-6 text-agro-600" />
              </div>
              <h3 className="font-medium text-agro-800">Expertise technique</h3>
              <p className="text-sm text-gray-600 mt-2">Formation pratique et théorique</p>
            </div>
            <div className="bg-white/90 backdrop-blur-md rounded-2xl md:rounded-r-2xl md:rounded-l-none p-6 shadow-xl flex flex-col items-center text-center">
              <div className="bg-agro-100 p-3 rounded-full mb-3">
                <Users className="h-6 w-6 text-agro-600" />
              </div>
              <h3 className="font-medium text-agro-800">Réseau professionnel</h3>
              <p className="text-sm text-gray-600 mt-2">Partenariats avec les acteurs du secteur</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Wave shape divider */}
      <div className="absolute bottom-0 left-0 w-full overflow-hidden">
        <svg
          className="relative block w-full h-12 md:h-16"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V120H0V0C115.39,13.93,252.2,46.79,321.39,56.44Z"
            className="fill-background"
          ></path>
        </svg>
      </div>
    </section>
  );
};

export default Hero;
